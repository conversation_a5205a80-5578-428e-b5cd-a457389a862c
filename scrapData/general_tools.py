import datetime
import json
from langchain_core.tools import tool


@tool
def get_current_date():
    """获取当日日期"""
    return datetime.datetime.today().strftime('%Y-%m-%d')


@tool
def save_dict(rights_data: dict):
    """
    保存格式为dict的权益数据，如果不传入save_path，则有默认的保存路径，数据格式参考以下内容：
    {
      "brand_name": "品牌名称",
      "service_rights": [
        {
          "title": "权益标题",
          "description": "详细描述",
        }
      ],
      "material_rights": [
        {
          "title": "权益标题",
          "description": "详细描述",
        }
      ],
      "other_rights": [
        {
          "title": "权益标题",
          "description": "详细描述",
        }
      ]
    }
    """
    save_path = rights_data["brand_name"] + "-" + datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S") + ".json"
    print(f"正在保存内容到{save_path}当中")
    with open(save_path, "w", encoding="utf8") as f:
        json.dump(rights_data, f, ensure_ascii=False, indent=4)
    return f"内容已成功保存到{save_path}当中"


if __name__ == '__main__':
    test_data = {
      "brand_name": "品牌名称",
      "service_rights": [
        {
          "title": "权益标题",
          "description": "详细描述",
        }
      ],
      "material_rights": [
        {
          "title": "权益标题",
          "description": "详细描述",
        }
      ],
      "other_rights": [
        {
          "title": "权益标题",
          "description": "详细描述",
        }
      ]
    }
    save_dict(test_data)