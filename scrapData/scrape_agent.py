import os
import json
import requests
from bs4 import BeautifulSoup
from typing import Optional, Dict
from openai import OpenAI
from dotenv import load_dotenv, find_dotenv
from prompt_templates import extract_prompt

_ = load_dotenv(find_dotenv())

class ScrapeAgent:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.client = OpenAI(
            api_key="sk-117fc6c9df5846d1b04c53113df3d043",
            base_url="https://api.deepseek.com"
        )


    def html_filter(self, html_content: str) -> str:
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            # 移除script和style标签
            for script in soup(["script", "style"]):
                script.decompose()
            # 获取文本内容
            text = soup.get_text()
            # 清理多余的空白字符
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            return text
        except:
            return html_content


    def scrape_url(self, url: str) -> Optional[Dict]:
        try:
            print(f"正在爬取网页源代码: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            html_content = self.html_filter(response.text)
            result = {
                'url': url,
                'status_code': response.status_code,
                'html_content': html_content,
                'content_length': len(response.text),
                'encoding': response.encoding,
                'success': True,
                'error': None
            }
            print(f"爬取成功: {url}, 内容长度: {result['content_length']} 字符")
            return result

        except requests.exceptions.RequestException as e:
            error_msg = f"爬取失败: {str(e)}"
            print(error_msg)
            return {
                'url': url,
                'status_code': None,
                'html_content': None,
                'content_length': 0,
                'encoding': None,
                'success': False,
                'error': error_msg
            }

    def llm_clean_html(self, html_content: str):
        input_promts = extract_prompt + "\n" + html_content
        messages = [
            {"role": "system", "content": input_promts},
        ]

        response = self.client.chat.completions.create(
            model="deepseek-chat",
            messages=messages
        )
        result_text = response.choices[0].message.content.strip()

        # 解析JSON结果
        result_text = result_text.strip().replace("```json", "").replace("```", "").strip()
        result = json.loads(result_text)
        return result


    def extract_from_url_pipeline(self, url: str):
        """爬取并通过LLM自动解析给定的URL的网页中的车企权益相关的数据并返回JSON格式"""
        result = self.scrape_url(url)
        result_dict = {"success": False}
        if result['success']:
            result_dict = self.llm_clean_html(result["html_content"])
        self.session.close()
        return result_dict


if __name__ == "__main__":
    scraper = ScrapeAgent()
    # test_url = "https://www.xiaomiev.com/rights"
    test_url = "https://hima.auto/wenjie/service/"
    result = scraper.extract_from_url_pipeline(test_url)
    print(result)

