from langgraph.checkpoint.memory import InMemorySaver
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model
from general_tools import get_current_date, save_dict
from scrape_agent import ScrapeAgent
from dotenv import load_dotenv, find_dotenv
from prompt_templates import interact_prompt
import os
_ = load_dotenv(find_dotenv())


class InteractAgent:
    def __init__(self, model_name="deepseek-chat", model_provider="openai", base_url="https://api.deepseek.com", api_key=None):
        # Set the API key if provided, otherwise use environment variable
        if api_key:
            os.environ["OPENAI_API_KEY"] = api_key
        elif not os.getenv("OPENAI_API_KEY"):
            raise ValueError("API key must be provided either as parameter or set in OPENAI_API_KEY environment variable")

        model = init_chat_model(model_name, model_provider=model_provider, base_url=base_url)
        self.checkpointer = InMemorySaver()
        self.agent = create_react_agent(
            model=model,
            tools=[get_current_date, save_dict, ScrapeAgent().extract_from_url_pipeline],
            prompt=interact_prompt,
            checkpointer=self.checkpointer
        )

    def chat(self, query, thread_id: int = 1):
        config = {
            "configurable": {
                "thread_id": str(thread_id)
            }
        }
        messages = {"messages": [{"role": "user", "content": query}]}
        result = self.agent.invoke(messages, config)
        return result["messages"][-1].content

if __name__ == "__main__":
    # Option 1: Use environment variable (set in .env file)
    agent = InteractAgent()

    # Option 2: Pass API key directly (uncomment and replace with your key)
    # agent = InteractAgent(api_key="your_deepseek_api_key_here")
    query1 = "帮我查询购买小米汽车有哪些权益？这是你需要查询的网址：https://www.xiaomiev.com/rights"
    resp = agent.chat(query1)
    print(resp)
    print("-" * 50)

    query2 = "今天几月几号？"
    resp = agent.chat(query2)
    print(resp)
    print("-" * 50)

    query3 = "那问界的呢？https://hima.auto/wenjie/service/"
    resp = agent.chat(query3)
    print(resp)
    print("-" * 50)


    query4 = "帮我保存一下小米的权益信息"
    resp = agent.chat(query4)
    print(resp)
    print("-" * 50)


