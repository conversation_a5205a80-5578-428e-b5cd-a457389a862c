import tiktoken
import pandas as pd
import numpy as np


def count_tokens_openai(text, model="gpt-4"):
    """
    使用OpenAI的方法计算文本内容的token数量
    支持不同的OpenAI模型
    """
    try:
        # 根据模型获取对应的编码器
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        # 如果模型不存在，使用默认的cl100k_base编码器（适用于GPT-4和GPT-3.5-turbo）
        print(f"警告: 模型 {model} 不存在，使用默认编码器 cl100k_base")
        encoding = tiktoken.get_encoding("cl100k_base")
    
    # 处理空值或非字符串值
    if pd.isna(text) or not isinstance(text, str):
        return 0
    
    # 编码文本并计算token数
    tokens = encoding.encode(text)
    token_count = len(tokens)
    
    return token_count


def count_tokens_by_encoding(text, encoding_name="cl100k_base"):
    """
    使用指定编码器计算token数量
    """
    encoding = tiktoken.get_encoding(encoding_name)
    
    # 处理空值或非字符串值
    if pd.isna(text) or not isinstance(text, str):
        return 0
        
    tokens = encoding.encode(text)
    return len(tokens)


# 读取CSV文件
csv_path = r"C:\Users\<USER>\Desktop\权益.csv"
df = pd.read_csv(csv_path)

# 确保output列存在
if 'output' not in df.columns:
    raise ValueError("CSV文件中没有找到'output'列")

# 计算每行的token数
print("\n计算每个模型的token数...")

# GPT-4
df['gpt4_tokens'] = df['output'].apply(lambda x: count_tokens_openai(x, "gpt-4"))
total_tokens_gpt4 = df['gpt4_tokens'].sum()
avg_tokens_gpt4 = df['gpt4_tokens'].mean()

# Qwen
df['qwen_tokens'] = df['output'].apply(lambda x: count_tokens_by_encoding(x, "cl100k_base"))
total_tokens_qwen = df['qwen_tokens'].sum()
avg_tokens_qwen = df['qwen_tokens'].mean()

# 打印结果
print("\n=== Token统计结果 ===")
print(f"\nGPT-4模型:")
print(f"总token数: {total_tokens_gpt4:,.0f}")
print(f"平均token数: {avg_tokens_gpt4:.2f}")

print(f"\nQwen-3-235B-A22B模型:")
print(f"总token数: {total_tokens_qwen:,.0f}")
print(f"平均token数: {avg_tokens_qwen:.2f}")

# 保存结果到新的CSV文件
output_path = csv_path.rsplit('.', 1)[0] + '_with_tokens.csv'
df.to_csv(output_path, index=False)
print(f"\n结果已保存到: {output_path}")